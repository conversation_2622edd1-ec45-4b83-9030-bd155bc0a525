import { ipc<PERSON>enderer, contextBridge } from 'electron'

// Import our type-safe API bridge
import { dbApi } from './api-bridge'

// --------- Expose some API to the Renderer process ---------
// Expose our database API first
contextBridge.exposeInMainWorld('db', dbApi);

// Expose sync and dialog APIs
contextBridge.exposeInMainWorld('electronAPI', {
  sync: dbApi.sync,
  settings: dbApi.settings,
  selectFolder: dbApi.selectFolder
});

// Then expose the ipcRenderer functions
contextBridge.exposeInMainWorld('ipcRenderer', {
  on(...args: Parameters<typeof ipcRenderer.on>) {
    const [channel, listener] = args
    return ipcRenderer.on(channel, (event, ...args) => listener(event, ...args))
  },
  off(...args: Parameters<typeof ipcRenderer.off>) {
    const [channel, ...omit] = args
    return ipcRenderer.off(channel, ...omit)
  },
  send(...args: Parameters<typeof ipcRenderer.send>) {
    const [channel, ...omit] = args
    return ipcRenderer.send(channel, ...omit)
  },
  invoke(...args: Parameters<typeof ipcRenderer.invoke>) {
    const [channel, ...omit] = args
    return ipcRenderer.invoke(channel, ...omit)
  },

  // You can expose other APIs you need here.
  // ...
})

// Window controls API
contextBridge.exposeInMainWorld('windowControls', {
  minimize: () => ipcRenderer.invoke('window:minimize'),
  maximize: () => ipcRenderer.invoke('window:maximize'),
  close: () => ipcRenderer.invoke('window:close')
})

// Legacy backup APIs have been removed - now using modern sync engine in /sync-logic folder

// All DOM/loading screen functionality removed - not needed