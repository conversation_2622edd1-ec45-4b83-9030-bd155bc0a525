<template>
  <Teleport to="body">
    <div class="modal-overlay" @click="handleOverlayClick">
      <div class="backup-history-modal" @click.stop>
        <div class="close-icon" @click="$emit('close')">
          <img src="/icons/close-icon.svg" alt="Close" />
        </div>

        <div class="modal-header">
          <h1 class="modal-title">Sync History</h1>
          <p class="modal-subtitle">View all sync operations including manual and auto syncs</p>
        </div>

        <div class="divider"></div>

        <div class="modal-content">
          <!-- Loading state -->
          <div v-if="isLoading" class="loading-state">
            <div class="loading-spinner"></div>
            <span class="loading-text">Loading sync history...</span>
          </div>

          <!-- Error state -->
          <div v-else-if="error" class="error-state">
            <div class="error-icon">⚠️</div>
            <h3 class="error-title">Failed to Load History</h3>
            <p class="error-message">{{ error }}</p>
            <button class="retry-button" @click="loadBackupHistory">
              Retry
            </button>
          </div>

          <!-- Empty state -->
          <div v-else-if="backupHistory.length === 0" class="empty-state">
            <div class="empty-icon">📋</div>
            <h3 class="empty-title">No Sync History</h3>
            <p class="empty-message">You haven't performed any syncs yet. Unified sync system pending implementation.</p>
          </div>

          <!-- History list -->
          <div v-else class="history-list">
            <div 
              v-for="backup in backupHistory" 
              :key="backup.id"
              class="history-item"
              :class="{ 
                'failed': backup.status === 'failed',
                'in-progress': backup.status === 'in_progress'
              }"
            >
              <div class="backup-status-indicator">
                <div 
                  class="status-dot" 
                  :class="getStatusClass(backup.status)"
                ></div>
              </div>

              <div class="backup-details">
                <div class="backup-header">
                  <div class="backup-type-wrapper">
                    <span class="backup-type">{{ formatBackupType(backup.backup_type) }}</span>
                    <span class="backup-status">{{ formatStatus(backup.status) }}</span>
                  </div>
                  <span class="backup-date">{{ formatDate(backup.created_at) }}</span>
                </div>

                <div class="backup-info">
                  <div class="backup-location">
                    <span class="location-label">Location:</span>
                    <span class="location-path">{{ backup.backup_location }}</span>
                  </div>
                </div>

                <div class="backup-stats">
                  <div class="stat-item">
                    <span class="stat-label">Items:</span>
                    <span class="stat-value">{{ backup.items_backed_up || 0 }}</span>
                  </div>
                  <div v-if="backup.errors_count > 0" class="stat-item error">
                    <span class="stat-label">Errors:</span>
                    <span class="stat-value">{{ backup.errors_count }}</span>
                  </div>
                  <div v-if="backup.completed_at" class="stat-item">
                    <span class="stat-label">Duration:</span>
                    <span class="stat-value">{{ formatDuration(backup.created_at, backup.completed_at) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <div class="footer-left">
            <span class="history-count">{{ backupHistory.length }} backup{{ backupHistory.length !== 1 ? 's' : '' }} shown</span>
          </div>
          <div class="footer-right">
            <button 
              class="btn btn-secondary" 
              @click="clearHistory"
              :disabled="backupHistory.length === 0 || isClearing"
            >
              <div v-if="isClearing" class="loading-spinner small"></div>
              <span v-else>Clear History</span>
            </button>
            <button class="btn btn-primary" @click="$emit('close')">
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import type { BackupMetadata } from '../../types/electron-api'

// Component state
const backupHistory = ref<BackupMetadata[]>([])
const isLoading = ref(true)
const isClearing = ref(false)
const error = ref<string | null>(null)

// Emits
const emit = defineEmits<{
  close: []
}>()

// TODO: Load sync history from unified sync API
async function loadBackupHistory() {
  try {
    isLoading.value = true
    error.value = null
    
    // TODO: Replace with unified sync history API
    // const history = await window.electronAPI.unifiedSync.getHistory(100)
    
    // Placeholder - show empty history for now
    backupHistory.value = []
  } catch (err: any) {
    console.error('Failed to load sync history:', err)
    error.value = 'Unified sync system pending implementation'
  } finally {
    isLoading.value = false
  }
}

// TODO: Clear sync history with unified sync API
async function clearHistory() {
  if (backupHistory.value.length === 0) return
  
  try {
    isClearing.value = true
    
    // TODO: Replace with unified sync clear history API
    // const result = await window.electronAPI.unifiedSync.clearHistory()
    
    // Placeholder - clear local array for now
    backupHistory.value = []
  } catch (err: any) {
    console.error('Failed to clear sync history:', err)
    error.value = 'Unified sync system pending implementation'
  } finally {
    isClearing.value = false
  }
}

// Handle overlay click to close modal
function handleOverlayClick(event: MouseEvent) {
  if (event.target === event.currentTarget) {
    emit('close')
  }
}

// Format backup type for display
function formatBackupType(type: string): string {
  switch (type) {
    case 'manual':
      return 'Manual Backup'
    case 'auto':
      return 'Auto Backup'
    default:
      return type.charAt(0).toUpperCase() + type.slice(1) + ' Backup'
  }
}

// Format status for display
function formatStatus(status: string): string {
  switch (status) {
    case 'completed':
      return 'Completed'
    case 'failed':
      return 'Failed'
    case 'in_progress':
      return 'In Progress'
    default:
      return status.charAt(0).toUpperCase() + status.slice(1)
  }
}

// Get CSS class for status
function getStatusClass(status: string): string {
  switch (status) {
    case 'completed':
      return 'success'
    case 'failed':
      return 'error'
    case 'in_progress':
      return 'pending'
    default:
      return 'unknown'
  }
}

// Format date for display
function formatDate(dateStr: string): string {
  const date = new Date(dateStr)
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  // If less than a minute ago
  if (diffInSeconds < 60) {
    return 'just now'
  }
  
  // If less than an hour ago
  if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60)
    return `${minutes}m ago`
  }
  
  // If less than a day ago
  if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600)
    return `${hours}h ago`
  }
  
  // If less than a week ago
  if (diffInSeconds < 604800) {
    const days = Math.floor(diffInSeconds / 86400)
    return `${days}d ago`
  }
  
  // Otherwise show full date
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
}

// Format duration between two timestamps
function formatDuration(startStr: string, endStr: string): string {
  const start = new Date(startStr)
  const end = new Date(endStr)
  const durationMs = end.getTime() - start.getTime()
  const durationSeconds = Math.floor(durationMs / 1000)

  if (durationSeconds < 60) {
    return `${durationSeconds}s`
  }
  
  const minutes = Math.floor(durationSeconds / 60)
  const seconds = durationSeconds % 60
  
  if (minutes < 60) {
    return seconds > 0 ? `${minutes}m ${seconds}s` : `${minutes}m`
  }
  
  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60
  
  return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`
}

// Load history on mount
onMounted(() => {
  loadBackupHistory()
})
</script>

<style scoped>
/* Apply Montserrat font to all elements */
.backup-history-modal,
.backup-history-modal * {
  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Modal Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-modal-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 15000;
  isolation: isolate;
  padding: 20px;
  box-sizing: border-box;
}

/* Main Modal Container */
.backup-history-modal {
  background-color: var(--color-modal-bg);
  border-radius: 16px;
  box-shadow: 0 8px 24px var(--color-card-shadow);
  width: 650px;
  max-width: 100%;
  max-height: calc(100vh - 40px);
  min-height: 300px;
  display: flex;
  flex-direction: column;
  font-family: 'Montserrat', sans-serif;
  overflow: hidden;
  position: relative;
}

/* Close Button */
.close-icon {
  position: absolute;
  top: 20px;
  right: 20px;
  cursor: pointer;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.close-icon:hover {
  background-color: var(--color-nav-item-hover);
}

.close-icon img {
  width: 24px;
  height: 24px;
}

/* Modal Header */
.modal-header {
  padding: 32px 32px 24px 32px;
  text-align: center;
  border-bottom: 1px solid var(--color-modal-border);
  flex-shrink: 0;
}

.modal-title {
  color: var(--color-text-primary);
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.modal-subtitle {
  color: var(--color-text-secondary);
  font-size: 14px;
  font-weight: 400;
  margin: 0;
  line-height: 1.4;
}

/* Divider */
.divider {
  height: 1px;
  background-color: var(--color-border-primary);
  width: 100%;
  flex-shrink: 0;
}

/* Modal Content */
.modal-content {
  padding: 24px 32px;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

/* Custom Scrollbar - same as notes list */
.modal-content::-webkit-scrollbar {
  width: 8px;
}

.modal-content::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
  border-radius: 0 8px 8px 0;
}

.modal-content::-webkit-scrollbar-thumb {
  background: var(--color-scrollbar-thumb);
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-scrollbar-thumb-hover);
}

/* States */
.loading-state,
.error-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  min-height: 300px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--color-border-primary);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 16px;
}

.loading-spinner.small {
  width: 16px;
  height: 16px;
  border-width: 2px;
  margin: 0;
}

.loading-text {
  color: var(--color-text-secondary);
  font-size: 14px;
}

.error-icon,
.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-title,
.empty-title {
  color: var(--color-text-primary);
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.error-message,
.empty-message {
  color: var(--color-text-secondary);
  font-size: 14px;
  margin: 0 0 24px 0;
  max-width: 400px;
  line-height: 1.4;
}

.retry-button {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
}

.retry-button:hover {
  background-color: var(--color-primary-hover);
}

/* History List */
.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--color-border-primary);
  background-color: var(--color-bg-secondary);
  transition: all 0.2s ease;
}

.history-item:hover {
  background-color: var(--color-bg-tertiary);
  border-color: var(--color-border-hover);
}

.history-item.failed {
  border-left: 4px solid var(--color-error);
}

.history-item.in-progress {
  border-left: 4px solid var(--color-warning, #f59e0b);
}

.backup-status-indicator {
  margin-right: 12px;
  margin-top: 4px;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-dot.success {
  background-color: var(--color-success, #22c55e);
}

.status-dot.error {
  background-color: var(--color-error);
}

.status-dot.pending {
  background-color: var(--color-warning, #f59e0b);
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.status-dot.unknown {
  background-color: var(--color-text-muted);
}

.backup-details {
  flex: 1;
  min-width: 0;
}

.backup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.backup-type-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.backup-type {
  color: var(--color-text-primary);
  font-size: 15px;
  font-weight: 600;
}

.backup-status {
  color: var(--color-text-secondary);
  font-size: 12px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
  background-color: var(--color-bg-tertiary);
}

.backup-date {
  color: var(--color-text-secondary);
  font-size: 13px;
  font-weight: 500;
  white-space: nowrap;
}

.backup-info {
  margin-bottom: 8px;
}

.backup-location {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 4px;
}

.location-label {
  color: var(--color-text-secondary);
  font-size: 12px;
  font-weight: 500;
  flex-shrink: 0;
}

.location-path {
  color: var(--color-text-primary);
  font-size: 12px;
  font-family: 'Courier New', monospace;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
}

.backup-stats {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-item.error .stat-label,
.stat-item.error .stat-value {
  color: var(--color-error);
}

.stat-label {
  color: var(--color-text-secondary);
  font-size: 11px;
  font-weight: 500;
}

.stat-value {
  color: var(--color-text-primary);
  font-size: 11px;
  font-weight: 600;
}

/* Modal Footer */
.modal-footer {
  padding: 24px 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid var(--color-border-primary);
  flex-shrink: 0;
  background-color: var(--color-bg-tertiary);
}

.footer-left {
  display: flex;
  align-items: center;
}

.footer-right {
  display: flex;
  gap: 12px;
}

.history-count {
  color: var(--color-text-secondary);
  font-size: 12px;
  font-weight: 500;
}

.btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  display: flex;
  align-items: center;
  gap: 6px;
  min-width: 80px;
  justify-content: center;
}

.btn-primary {
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--color-primary-hover);
}

.btn-secondary {
  background-color: var(--color-btn-secondary-bg);
  color: var(--color-btn-secondary-text);
  border: 1px solid var(--color-btn-secondary-border);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--color-btn-secondary-hover);
}

.btn:disabled {
  background-color: var(--color-border-primary);
  color: var(--color-text-muted);
  cursor: not-allowed;
}

/* Animations */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .backup-history-modal {
    width: 95%;
    max-height: 95vh;
  }

  .modal-header,
  .modal-content,
  .modal-footer {
    padding: 16px 20px;
  }

  .backup-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .backup-date {
    align-self: flex-end;
  }

  .backup-stats {
    gap: 12px;
  }

  .modal-footer {
    flex-direction: column;
    gap: 12px;
  }

  .footer-left,
  .footer-right {
    width: 100%;
    justify-content: center;
  }
}
</style> 