import { SyncManifest, SyncItem } from './types';
import { fileOperations } from './file-operations';
import { manifestManager } from './manifest-manager';
import { unifiedSyncEngine } from './unified-sync-engine';
import * as path from 'path';
import * as fs from 'fs/promises';
import * as crypto from 'crypto';

interface ParsedStructure {
  books: Map<string, {
    id: string;
    title: string;
    folders: Map<string, ParsedFolder>;
    notes: ParsedNote[];
  }>;
  standaloneNotes: ParsedNote[];
  standaloneFolders: Map<string, ParsedFolder>;
}

interface ParsedFolder {
  id: string;
  name: string;
  path: string;
  notes: ParsedNote[];
  subfolders: Map<string, ParsedFolder>;
}

interface ParsedNote {
  id: string;
  title: string;
  path: string;
  metadata?: any;
}

class ImportHandler {
  /**
   * Import a backup directory, detecting its type and processing accordingly
   */
  async importBackup(directory: string): Promise<void> {
    console.log(`[ImportHandler] Starting import from: ${directory}`);
    
    const backupType = await this.detectBackupType(directory);
    
    if (backupType === 'noti') {
      console.log('[ImportHandler] Detected Noti backup, running normal sync');
      // It's already a Noti backup, just run sync
      await unifiedSyncEngine.sync(directory);
      return;
    }
    
    console.log('[ImportHandler] Detected raw files, parsing directory structure');
    // Parse raw directory structure and create manifest
    const structure = await this.parseDirectoryStructure(directory);
    const manifest = await this.createManifestFromStructure(structure, directory);
    
    // Save manifest to the directory
    await manifestManager.saveManifest(directory, manifest);
    
    console.log('[ImportHandler] Created manifest, running sync');
    // Now run sync with the newly created manifest
    await unifiedSyncEngine.sync(directory);
  }
  
  /**
   * Detect if directory is a Noti backup or raw files
   */
  private async detectBackupType(directory: string): Promise<'noti' | 'raw'> {
    try {
      const manifestPath = path.join(directory, 'sync-manifest.json');
      await fs.access(manifestPath);
      return 'noti';
    } catch {
      return 'raw';
    }
  }
  
  /**
   * Parse directory structure recursively
   */
  private async parseDirectoryStructure(directory: string): Promise<ParsedStructure> {
    const structure: ParsedStructure = {
      books: new Map(),
      standaloneNotes: [],
      standaloneFolders: new Map()
    };
    
    // Check for Books directory
    const booksPath = path.join(directory, 'Books');
    try {
      await fs.access(booksPath);
      await this.parseBooksDirectory(booksPath, structure);
    } catch {
      console.log('[ImportHandler] No Books directory found');
    }
    
    // Parse root level items
    await this.parseDirectory(directory, structure, null, null);
    
    return structure;
  }
  
  /**
   * Parse Books directory structure
   */
  private async parseBooksDirectory(booksPath: string, structure: ParsedStructure, manifest?: any): Promise<void> {
    const entries = await fs.readdir(booksPath, { withFileTypes: true });
    
    for (const entry of entries) {
      if (!entry.isDirectory()) continue;
      
      const bookPath = path.join(booksPath, entry.name);
      
      // Try to get book metadata from manifest first
      let bookMeta: any = null;
      if (manifest) {
        const bookItem = manifest.items.find((item: any) => 
          item.type === 'book' && item.name === entry.name
        );
        bookMeta = bookItem?.metadata;
      }
      
      // Fallback to .book-meta.json for backward compatibility
      if (!bookMeta) {
        const bookMetaPath = path.join(bookPath, '.book-meta.json');
        try {
          const metaContent = await fs.readFile(bookMetaPath, 'utf-8');
          bookMeta = JSON.parse(metaContent);
        } catch {
          console.log(`[ImportHandler] No book metadata found for: ${entry.name}`);
        }
      }
      
      const bookId = bookMeta?.id ? this.parseItemId(bookMeta.id) : this.generateItemId('book', entry.name, bookPath);
      const bookTitle = bookMeta?.title || entry.name;
      
      const book = {
        id: String(bookId), // Keep as string for internal tracking
        title: bookTitle,
        folders: new Map<string, ParsedFolder>(),
        notes: [],
        metadata: bookMeta // Store full metadata
      };
      
      // Parse book contents
      await this.parseBookContents(bookPath, book);
      
      structure.books.set(String(bookId), book);
    }
  }
  
  /**
   * Parse contents of a book directory
   */
  private async parseBookContents(bookPath: string, book: any): Promise<void> {
    const entries = await fs.readdir(bookPath, { withFileTypes: true });
    
    for (const entry of entries) {
      const entryPath = path.join(bookPath, entry.name);
      
      // Handle hidden cover files
      if (entry.name === '.cover.jpg' && entry.isFile()) {
        // Store cover file path for later processing
        book.coverPath = entryPath;
        continue;
      }
      
      // Skip other hidden files and .book-meta.json
      if (entry.name.startsWith('.') || entry.name === '.book-meta.json') continue;
      
      if (entry.isDirectory()) {
        const folder = await this.parseFolder(entryPath, entry.name);
        book.folders.set(folder.id, folder);
      } else if (entry.name.endsWith('.md')) {
        const note = await this.parseNote(entryPath);
        if (note) book.notes.push(note);
      }
    }
  }
  
  /**
   * Parse a folder recursively
   */
  private async parseFolder(folderPath: string, folderName: string): Promise<ParsedFolder> {
    const folder: ParsedFolder = {
      id: String(this.generateItemId('folder', folderName, folderPath)), // Keep as string for internal tracking
      name: folderName,
      path: folderPath,
      notes: [],
      subfolders: new Map()
    };
    
    const entries = await fs.readdir(folderPath, { withFileTypes: true });
    
    for (const entry of entries) {
      const entryPath = path.join(folderPath, entry.name);
      
      if (entry.name.startsWith('.')) continue;
      
      if (entry.isDirectory()) {
        const subfolder = await this.parseFolder(entryPath, entry.name);
        folder.subfolders.set(subfolder.id, subfolder);
      } else if (entry.name.endsWith('.md')) {
        const note = await this.parseNote(entryPath);
        if (note) folder.notes.push(note);
      }
    }
    
    return folder;
  }
  
  /**
   * Parse a note file
   */
  private async parseNote(notePath: string): Promise<ParsedNote | null> {
    try {
      const noteTitle = path.basename(notePath, '.md');
      const metadata = await this.extractNoteMetadata(notePath);
      
      return {
        id: String(metadata?.id ? this.parseItemId(metadata.id) : this.generateItemId('note', noteTitle, notePath)), // Keep as string for internal tracking
        title: metadata?.title || noteTitle,
        path: notePath,
        metadata
      };
    } catch (error) {
      console.error(`[ImportHandler] Error parsing note ${notePath}:`, error);
      return null;
    }
  }
  
  /**
   * Parse general directory (not Books)
   */
  private async parseDirectory(
    dir: string, 
    structure: ParsedStructure, 
    parentBook: string | null,
    parentFolder: ParsedFolder | null
  ): Promise<void> {
    const entries = await fs.readdir(dir, { withFileTypes: true });
    
    for (const entry of entries) {
      const entryPath = path.join(dir, entry.name);
      
      // Skip special directories and hidden files
      if (entry.name === 'Books' || entry.name.startsWith('.') || entry.name === 'sync-manifest.json') {
        continue;
      }
      
      if (entry.isDirectory()) {
        // Only process as standalone if at root level
        if (!parentBook && !parentFolder && dir === path.dirname(entryPath)) {
          const folder = await this.parseFolder(entryPath, entry.name);
          structure.standaloneFolders.set(folder.id, folder);
        }
      } else if (entry.name.endsWith('.md')) {
        // Only process as standalone if at root level
        if (!parentBook && !parentFolder && dir === path.dirname(entryPath)) {
          const note = await this.parseNote(entryPath);
          if (note) structure.standaloneNotes.push(note);
        }
      }
    }
  }
  
  /**
   * Create sync manifest from parsed structure
   */
  private async createManifestFromStructure(structure: ParsedStructure, backupPath: string): Promise<SyncManifest> {
    const items: SyncItem[] = [];
    const timestamp = new Date().toISOString();
    
    // Process books
    for (const [bookId, book] of structure.books.entries()) {
      items.push({
        id: this.parseItemId(bookId),
        type: 'book',
        item_id: this.parseItemId(bookId),
        path: `Books/${book.title}/`,
        is_active: true,
        hash: crypto.createHash('md5').update(book.title).digest('hex'),
        created_at: timestamp,
        updated_at: timestamp
      });
      
      // Process book's folders
      for (const [folderId, folder] of book.folders) {
        items.push(...this.createFolderItems(folder, bookId, null, timestamp));
      }
      
      // Process book's root notes
      for (const note of book.notes) {
        const noteContent = await fs.readFile(note.path, 'utf-8');
        items.push({
          id: this.parseItemId(note.id),
          type: 'note',
          item_id: this.parseItemId(note.id),
          path: `Books/${book.title}/${note.title}.md`,
          is_active: true,
          hash: crypto.createHash('md5').update(noteContent).digest('hex'),
          created_at: note.metadata?.createdAt || timestamp,
          updated_at: note.metadata?.updatedAt || timestamp
        });
      }
    }
    
    // Process standalone folders
    for (const [folderId, folder] of structure.standaloneFolders.entries()) {
      items.push(...this.createFolderItems(folder, null, null, timestamp));
    }
    
    // Process standalone notes
    for (const note of structure.standaloneNotes) {
      const noteContent = await fs.readFile(note.path, 'utf-8');
      items.push({
        id: this.parseItemId(note.id),
        type: 'note',
        item_id: this.parseItemId(note.id),
        path: `${note.title}.md`,
        is_active: true,
        hash: crypto.createHash('md5').update(noteContent).digest('hex'),
        created_at: note.metadata?.createdAt || timestamp,
        updated_at: note.metadata?.updatedAt || timestamp
      });
    }
    
    return {
      version: 1,
      deviceId: 'import',
      lastSync: timestamp,
      items: items as any[], // Convert SyncItem[] to ManifestItem[]
      deletions: []
    };
  }
  
  /**
   * Create items for a folder and its contents recursively
   */
  private createFolderItems(
    folder: ParsedFolder, 
    bookId: string | null, 
    parentId: string | null,
    timestamp: string
  ): SyncItem[] {
    const items: SyncItem[] = [];
    
    // Create folder item
    items.push({
      id: this.parseItemId(folder.id),
      type: 'folder',
      item_id: this.parseItemId(folder.id),
      path: folder.path,
      is_active: true,
      hash: crypto.createHash('md5').update(folder.name).digest('hex'),
      created_at: timestamp,
      updated_at: timestamp
    });
    
    // Process folder's notes
    for (const note of folder.notes) {
      items.push({
        id: this.parseItemId(note.id),
        type: 'note', // Fixed: was 'item_type', should be 'type'
        item_id: this.parseItemId(note.id),
        path: `${folder.path}/${note.title}.md`,
        is_active: true,
        hash: crypto.createHash('md5').update(note.title).digest('hex'),
        created_at: note.metadata?.createdAt || timestamp,
        updated_at: note.metadata?.updatedAt || timestamp
      });
    }
    
    // Process subfolders
    for (const [subfolderId, subfolder] of folder.subfolders.entries()) {
      items.push(...this.createFolderItems(subfolder, bookId, folder.id, timestamp));
    }
    
    return items;
  }
  
  /**
   * Generate consistent ID for items without existing IDs
   */
  private generateItemId(type: string, name: string, itemPath: string): string {
    // Use a combination of type, name, and path to generate consistent IDs
    const hash = require('crypto').createHash('md5');
    hash.update(`${type}:${name}:${itemPath}`);
    return hash.digest('hex').substring(0, 16);
  }
  
  /**
   * Convert a hash string to a consistent numeric ID
   */
  private hashToNumericId(hash: string): number {
    // Convert first 8 characters of hex to a number
    const numericId = parseInt(hash.substring(0, 8), 16);
    
    // Ensure the ID is positive and starts from a high range to avoid conflicts
    // with auto-incremented IDs from the database (which typically start from 1)
    return 1000000000 + (numericId % 1000000000);
  }
  
  /**
   * Helper to safely parse ID from various formats
   * Handles both numeric IDs and legacy string IDs with prefixes
   */
  private parseItemId(itemId: string | number): number {
    // If already a number, return it
    if (typeof itemId === 'number') {
      return itemId;
    }
    
    // If it's a string, try to parse it
    if (typeof itemId === 'string') {
      // Remove common prefixes like 'book_', 'note_', 'folder_'
      const cleanId = itemId.replace(/^(book_|note_|folder_)/, '');
      
      // Try to parse as number
      const parsed = parseInt(cleanId, 10);
      
      // If parsing failed or resulted in NaN, generate a consistent numeric ID
      if (isNaN(parsed)) {
        // Use the hash-to-numeric conversion for non-numeric IDs
        return this.hashToNumericId(cleanId);
      }
      
      return parsed;
    }
    
    // Fallback: generate a hash and convert to numeric
    const hash = this.generateItemId('unknown', String(itemId), String(itemId));
    return this.hashToNumericId(hash);
  }
  
  /**
   * Extract note metadata from .noti.json if exists
   */
  private async extractNoteMetadata(notePath: string): Promise<any | null> {
    try {
      const metaPath = notePath.replace('.md', '.noti.json');
      const metaContent = await fs.readFile(metaPath, 'utf-8');
      return JSON.parse(metaContent);
    } catch {
      // No metadata file exists
      return null;
    }
  }
  
  /**
   * Infer book from directory path structure
   */
  private inferBookFromPath(itemPath: string): string | null {
    const pathParts = itemPath.split(path.sep);
    const booksIndex = pathParts.indexOf('Books');
    
    if (booksIndex !== -1 && booksIndex < pathParts.length - 1) {
      return pathParts[booksIndex + 1];
    }
    
    return null;
  }
}

// Export singleton instance
export const importHandler = new ImportHandler();
export { ImportHandler };