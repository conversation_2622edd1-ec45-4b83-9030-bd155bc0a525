# Sync Structure Corruption Investigation

## Issue Description
When syncing a backup with an empty database, the folder structure is corrupted:
- Folders not linked to books suddenly become linked to books
- Notes in root are deleted
- Incorrect rename detection causes folders to be moved/merged incorrectly

## Important Constraint
**Folders linked to books MUST always be inside the "Books" folder.** This is by design and the forced parent assignment is correct behavior.

## Root Causes Identified

### 1. ~~Forced Parent Assignment Logic~~ (NOT THE ISSUE)
**Location:** `unified-sync-engine.ts` lines 903-919

The logic that forces folders with `book_id` to be under "Books/" folder is CORRECT. The issue is that standalone folders are incorrectly getting `book_id` relationships assigned.

### 2. Incorrect Relationship Assignment (CRITICAL)
**The Real Problem:** Standalone folders like "Fortnite" are somehow getting `book_id` relationships when they shouldn't have them.

This happens because:
- During import, false rename detection matches "Fortnite" with "Bombo's Big Question"
- The folder inherits the book relationship from the incorrect match
- The system then correctly moves it under Books/ (following the rule)

### 3. False Rename Detection
**Location:** `unified-sync-engine.ts` folder/book/note exists checking

The system checks for existing items by ID first, then falls back to name matching:
```typescript
let existingFolder = !isNaN(folderId) ? await this.folderExistsById(folderId) : null;
if (!existingFolder) {
  existingFolder = await this.folderExists(folderName, parentId, bookId);
}
```

**Problem:** When IDs don't match, unrelated items with similar names are considered the same, causing false renames and relationship inheritance.

### 4. Manifest Relationship Preservation
When a folder is incorrectly matched with another folder that has a book relationship, it inherits that relationship, causing structural corruption.

## Reproduction Example

Original structure:
```
Books/
├── Bombo's Big Question/  (has book_id)
│   └── Hellooooooooooo.md
Fortnite/                  (standalone, no book_id)
└── Fortnite2/
    └── fortnite3/
        └── Untitled Note.md
```

What happens during import:
1. "Fortnite" is falsely matched with existing folder (possibly "Bombo's Big Question" subfolder)
2. Inherits book_id relationship
3. Gets correctly moved under Books/ (following the rule)
4. Results in corrupted structure

## Fix Options

### Option 1: Fix Relationship Detection (Recommended)
Ensure folders only get book_id if they're actually book folders:

```typescript
// During import in unified-sync-engine.ts
if (item.relationships?.bookId) {
  // Verify this folder is actually under a book path in the manifest
  if (!item.path.startsWith('Books/')) {
    console.warn(`Folder ${item.name} has book relationship but not under Books/ in manifest, clearing relationship`);
    bookId = null;
  }
}
```

**Pros:** 
- Prevents incorrect relationship assignment
- Preserves correct structure
- Simple validation

### Option 2: Stricter Name Matching
Prevent false matches between unrelated items:

```typescript
private async folderExists(name: string, parentId: number | null, bookId: number | null): Promise<Folder | null> {
  // Only match if ALL criteria match, not just name
  let query = `SELECT * FROM folders WHERE name = ?`;
  const params: any[] = [name];
  
  // Must match parent relationship
  if (parentId !== null) {
    query += ` AND parent_id = ?`;
    params.push(parentId);
  } else {
    query += ` AND parent_id IS NULL`;
  }
  
  // Must match book relationship
  if (bookId !== null) {
    query += ` AND book_id = ?`;
    params.push(bookId);
  } else {
    query += ` AND book_id IS NULL`;
  }
  
  return await dbGet<Folder>(query, params);
}
```

### Option 3: Path-Based Validation
Use manifest paths to determine correct relationships:

```typescript
// Validate relationships based on path structure
private validateFolderRelationships(item: ManifestItem): void {
  if (item.type !== 'folder') return;
  
  // If under Books/, must have book relationship
  if (item.path.startsWith('Books/') && !item.relationships?.bookId) {
    console.error(`Folder at ${item.path} is under Books/ but has no book relationship`);
  }
  
  // If not under Books/, must NOT have book relationship
  if (!item.path.startsWith('Books/') && item.relationships?.bookId) {
    console.error(`Folder at ${item.path} has book relationship but not under Books/`);
    // Clear the incorrect relationship
    delete item.relationships.bookId;
  }
}
```

### Option 4: Import Order with Validation
Import items in strict order with relationship validation:

```typescript
// 1. Import all books first
// 2. Import folders, validating book relationships against imported books
// 3. For each folder with book_id, verify:
//    - The book exists
//    - The folder path starts with Books/
//    - No false matches occurred
// 4. Import notes with validated relationships
```

## Recommended Implementation

### Immediate Fix (Phase 1):
1. Add path-based validation (Option 3) to prevent incorrect book relationships
2. Implement stricter name matching (Option 2) to prevent false matches
3. Add logging to track when relationships are assigned/cleared

### Robust Fix (Phase 2):
1. Implement comprehensive relationship validation (Option 1)
2. Add pre-import validation of manifest relationships
3. Add post-import structure verification

### Testing Focus:
1. Ensure standalone folders never get book_id relationships
2. Verify folders under Books/ always have correct book_id
3. Test false rename detection prevention
4. Validate final structure matches original

## Key Insight
The forced parent assignment to Books folder is CORRECT behavior. The bug is that standalone folders are incorrectly receiving book_id relationships through false matching, which then triggers the correct behavior of moving them under Books/.

## Files to Modify
- `electron/main/api/sync-logic/unified-sync-engine.ts` (relationship validation)
- `electron/main/api/sync-logic/import-handler.ts` (path validation)
- `electron/main/api/sync-logic/change-detector.ts` (matching logic)